<template>
    <span>
        <span v-for="(iconObj, index) in icons" :key="index" :class="[iconObj.css]" :title="iconObj.tips">
            {{ iconObj.label }}
        </span>
    </span>

</template>

<script>
import { imageStandardIcon } from '../lib/common_base'

export default {
    name: 'ImageStandardIcons',
    props: {
        item: {
            type: Object,
            required: true
        }
    },
    computed: {
        icons() {
            try {
                const list = imageStandardIcon(this.item)
                return Array.isArray(list) ? list : []
            } catch (e) {
                return []
            }
        }
    }
}
</script>

<style scoped>
</style>


