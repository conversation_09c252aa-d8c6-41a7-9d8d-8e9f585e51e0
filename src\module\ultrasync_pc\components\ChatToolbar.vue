<template>
    <div class="toolbar clearfix">
        <el-popover
            placement="bottom"
            trigger="hover"
            popper-class="toolbar_item"
            :disabled="false"
        >
            <span>{{$t('emoticon')}}</span>
            <i @click.stop="emitShowFacePanel" class="fl icon iconfont iconsmile" slot="reference"></i>
        </el-popover>
        <el-popover
            placement="bottom"
            trigger="hover"
            popper-class="toolbar_item"
        >
            <input :key="fileTag" type="file" @change="onFileChange($event)" ref="uploadInput" multiple="multiple" :accept="supportFileTypeStrings">
            <span>{{$t('upload_file')}}</span>
            <i class="fl icon iconfont iconfolder" slot="reference" @click="uploadPicture"></i>
        </el-popover>
        <el-popover
            placement="bottom"
            trigger="hover"
            popper-class="toolbar_item"
            v-show="!isRecording"
            v-if="!isInternalNetworkEnv"
        >
            <span>{{$t('voice_recording')}}</span>
            <i @click="$emit('send-record-voice')" class="fl icon iconfont iconsoundlight" slot="reference"></i>
        </el-popover>
        <el-popover
            placement="bottom"
            trigger="hover"
            popper-class="toolbar_item"
            v-show="isRecording"
            v-if="!isInternalNetworkEnv"
        >
            <span>{{$t('voice_recording')}}</span>
            <i class="fl icon iconfont iconmicrophone" slot="reference"></i>
        </el-popover>
        <el-popover
            placement="bottom"
            trigger="hover"
            popper-class="toolbar_item"
            v-permission="{regionPermissionKey: 'live'}"
            v-if="existLive&&!isConferenceAuxOnline"
        >
            <span>{{$t('live')}}</span>
            <i  @click="$emit('accept-live-conference')" class="fl icon iconfont icontv" slot="reference"></i>
        </el-popover>
        <el-popover
            placement="bottom"
            trigger="hover"
            popper-class="toolbar_item"
            v-if="isShowCallVideoBtn"
        >
            <span>{{$t('conference_seeding')}}</span>
            <i @click="$emit('start-conference')" class="fl icon iconfont iconchaoshengbo" slot="reference"></i>
        </el-popover>
        <el-popover
            placement="bottom"
            trigger="hover"
            popper-class="toolbar_item"
            v-show="chatType===CHAT_TYPE['CHAT_WINDOW']"
        >
            <span>{{$t('more_features')}}</span>
            <i @click.stop="$emit('show-group-setting')" class="fl icon iconfont iconplus1" slot="reference"></i>
        </el-popover>
        <el-popover
            placement="bottom"
            trigger="hover"
            popper-class="toolbar_item"
            v-show="chatType===CHAT_TYPE['CHAT_WINDOW']&&isShowExamType"
        >
            <span>{{$t('exam_view_mode')}}</span>
            <i @click="$emit('toggle-page-type')" class="fl icon iconfont iconlist" slot="reference"></i>
        </el-popover>
        <el-popover
            placement="bottom"
            trigger="hover"
            popper-class="toolbar_item"
            v-permission="{regionPermissionKey: 'obstetricalAI'}"
            v-show="chatType===CHAT_TYPE['CHAT_WINDOW']&&isObstetricQCMulticenter"
        >
            <span>{{$t('obstetric_qc_multicenter')}}</span>
            <i @click="$emit('go-multicenter')" class="fl icon iconfont iconfenbushishujuku" slot="reference"></i>
        </el-popover>
        <el-popover
            placement="bottom"
            trigger="hover"
            popper-class="toolbar_item"
            v-permission="{regionPermissionKey: 'drAIAssistant'}"
            v-show="chatType===CHAT_TYPE['CHAT_WINDOW']&&isDrAiAnalyze&&$store.state.device.isIStationInfoDR&&$store.state.device.drConnectStatus"
        >
            <span>{{$t('dr_ai_analyze_statistics')}}</span>
            <i @click="$emit('go-dr-ai-statistics')" class="fl icon iconfont iconshanxingzhanbi" slot="reference"></i>
        </el-popover>
        <el-popover
            placement="bottom"
            trigger="hover"
            popper-class="toolbar_item"
            v-show="chatType===CHAT_TYPE['CHAT_WINDOW']"
        >
            <span>{{$t('chat_history')}}</span>
            <i @click="$emit('open-history')" class="fr icon iconfont iconyidiandiantubiao18" slot="reference"></i>
        </el-popover>
        <el-popover
            placement="bottom"
            trigger="hover"
            popper-class="toolbar_item"
            v-show="chatType===CHAT_TYPE['CHAT_WINDOW']"
        >
            <span>{{$t('clear_history')}}</span>
            <i @click="$emit('clear-history')" class="fr icon iconfont iconicon-clearicon" slot="reference"></i>
        </el-popover>
        <el-popover
            placement="bottom"
            trigger="click"
            width="250"
            v-show="chatType===CHAT_TYPE['CONFERENCE']"
            popper-class="toolbar_item_attendee"
        >
            <AttendeeList :list="attendeeArray" :cid="cid"></AttendeeList>
            <i class="fl icon iconfont icongroups" slot="reference"></i>
        </el-popover>
        <el-popover
            placement="bottom"
            trigger="hover"
            popper-class="toolbar_item"
            v-if="showMangerStopConference&&!showStopConference"
        >
            <span>{{$t('close_consultation')}}</span>
            <i @click="$emit('force-stop-conference')" class="icon iconfont iconshut-down" slot="reference">
            </i>
        </el-popover>
        <el-popover
            placement="bottom"
            trigger="hover"
            popper-class="toolbar_item"
            v-show="chatType===CHAT_TYPE['CHAT_WINDOW']&&EnableQc_statistics&&isGroupChat"
        >
            <span>{{$t('bi_data_display')}}</span>
            <i @click="$emit('open-bi-data')" class="fl icon iconfont icona-statisticalviewhistogram-line" slot="reference"></i>
        </el-popover>
        <el-popover
            placement="bottom"
            trigger="hover"
            popper-class="toolbar_item"
            v-show="chatType===CHAT_TYPE['CHAT_WINDOW']"
        >
            <span>{{$t('cloud_exam')}}</span>
            <i @click="$emit('open-cloud-exam')" class="fl icon iconfont iconzaixiankaoshi" slot="reference"></i>
        </el-popover>
    </div>
</template>

<script>
import AttendeeList from './attendeeList.vue'
import { CHAT_TYPE } from '../lib/constants.js'

export default {
    name: 'ChatToolbar',
    components: { AttendeeList },
    props: {
        cid: { type: [String, Number], required: true },
        chatType: { type: Number, required: true },
        isInternalNetworkEnv: { type: [Boolean, Number], default: false },
        isRecording: { type: [Boolean, Number], default: false },
        existLive: { type: [Boolean, Number], default: false },
        isConferenceAuxOnline: { type: [Boolean, Number], default: false },
        isShowCallVideoBtn: { type: [Boolean, Number], default: false },
        isShowExamType: { type: [Boolean, Number], default: false },
        isObstetricQCMulticenter: { type: [Boolean, Number], default: false },
        isDrAiAnalyze: { type: [Boolean, Number], default: false },
        EnableQc_statistics: { type: [Boolean, Number], default: false },
        isGroupChat: { type: [Boolean, Number], default: false },
        supportFileTypeStrings: { type: String, default: '' },
        fileTag: { type: [String, Number], default: '' },
        attendeeArray: { type: Array, default: () => [] },
        showMangerStopConference: { type: [Boolean, Number], default: false },
        showStopConference: { type: [Boolean, Number], default: false }
    },
    data(){
        return { CHAT_TYPE }
    },
    methods: {
        uploadPicture(){
            if(this.$refs.uploadInput){
                this.$refs.uploadInput.click()
            }
        },
        onFileChange(e){
            const files = e && e.target ? e.target.files : []
            this.$emit('upload-picture-change', files)
        },
        emitShowFacePanel(){
            this.$emit('show-face-panel')
        }
    }
}
</script>

<style scoped>
</style>


