<template>
	<div class="chat_mode" :key="'chat_mode_'+cid" :class="{gallery_type:chatType===CHAT_TYPE['GALLERY'],live_type:chatType===CHAT_TYPE['CONFERENCE'],base_gallery_type:chatType===CHAT_TYPE['BASE_GALLERY']}" >
        <div class="header longwrap" v-show="chatType===CHAT_TYPE['GALLERY']||chatType===CHAT_TYPE['CONFERENCE']||chatType===CHAT_TYPE['BASE_GALLERY']">{{conversation.subject}}</div>
        <chatMessageList
            ref="chat_message_list"
            :chatMessageList="chatMessageList"
            :customClass="['scroller',isConferenceAuxOnline?'showPadding50':'']"
            :chatType="chatType"
            :cid="cid"
            ></chatMessageList>
        <div class="chat_editer">
            <!-- <div class="videos">
                <audio v-for="(item,index) of realtimeVoiceAudios" :ref="'audio_'+index" autoplay="autoplay" :id="item.volume==0?'self_sound':item.audio_id" :key="item.audio_id"></audio>
            </div> -->
            <ChatToolbar
                :cid="cid"
                :chatType="chatType"
                :is-internal-network-env="isInternalNetworkEnv"
                :is-recording="isRecording"
                :exist-live="existLive"
                :is-conference-aux-online="isConferenceAuxOnline"
                :is-show-call-video-btn="isShowCallVideoBtn"
                :is-show-exam-type="isShowExamType"
                :is-obstetric-q-c-multicenter="isObstetricQCMulticenter"
                :is-dr-ai-analyze="isDrAiAnalyze"
                :enable-qc_statistics="EnableQc_statistics"
                :is-group-chat="isGroupChat"
                :support-file-type-strings="supportFileTypeStrings"
                :file-tag="file_tag"
                :attendee-array="attendeeArray"
                :show-manger-stop-conference="showMangerStopConference"
                :show-stop-conference="showStopConference"
                @show-face-panel="showFacePanel"
                @upload-picture-change="uploadPictureStart"
                @send-record-voice="sendRecordVoice"
                @accept-live-conference="acceptLiveConference"
                @start-conference="clickStartConferenceFn"
                @show-group-setting="showGroupSetting"
                @toggle-page-type="togglePageType"
                @go-multicenter="goToMulticenter"
                @go-dr-ai-statistics="goToDrAiAnalyzeStatistics"
                @open-history="openChatHistorySearch"
                @clear-history="clearHistory"
                @force-stop-conference="forceStopConference"
                @open-bi-data="openBIDataShow"
                @open-cloud-exam="openCloudExam"
            />
            <!-- <div v-show="isShowFacePanel" class="face_panel">
                <img @click.stop="appendFace" v-for="(num,index) in faceArr" :src="`static/resource_pc/images/face/0/emo_${num}.gif`" :key="index">
            </div> -->
            <div v-show="isShowFacePanel" class="face_panel">
                <span @click.stop="appendFace(emoji)" v-for="(emoji, index) in emojiArr" :key="index" class="emoji">
                    {{ emoji }}
                </span>
            </div>
            <div v-show="isRecording" class="recording_panel">
                <div class="record_count">
                    <i class="icon iconfont iconSound-wave"></i>
                    <span>{{recordTimeStr}}</span>
                    <i class="icon iconfont iconSound-wave"></i>
                </div>

                <i @click="recordCancel" class="icon iconfont iconel-icon-delete2"></i>
                <i @click="recordEnd" class="icon iconfont iconsend"></i>

            </div>
            <span v-if="isConferenceAuxOnline" class="realtime_toolbar_triangle"></span>
            <!--  -->
            <div class="realtime_toolbar" v-if="isConferenceAuxOnline">
                <el-popover
                    placement="top"
                    trigger="hover"
                    popper-class="toolbar_item"
                    v-show="isConferenceAuxOnline"
                    >
                    <span>{{$t('live_address')}}</span>
                    <i @click="getLiveAddress"  class="icon iconfont iconlocation" slot="reference">
                    </i>
                </el-popover>
                <el-popover
                    placement="top"
                    trigger="hover"
                    popper-class="toolbar_item"
                    v-show="isConferenceRecording"
                    >
                    <span >{{$t('is_recording_text')}}</span>
                    <i  class="icon iconfont iconagora_luzhizhong" slot="reference"></i>
                </el-popover>
            </div>
            <div v-show="isShowGroupSetting" class="group_setting_panel">
                <el-popover
                    placement="top"
                    trigger="hover"
                    popper-class="toolbar_item"
                    v-if="isWorkStation&&!isService&&!globalParams.isCE&&!isInternalNetworkEnv"
                    >
                    <span>{{$t('send_uf_to_conversation')}}</span>
                    <i @click="fileTransfer" class="icon iconfont icon_chaoshenghuizhenshenqing fl" slot="reference"></i>
                </el-popover>
                <el-popover
                    placement="top"
                    trigger="hover"
                    popper-class="toolbar_item"
                    v-if="isWorkStation&&!isService&&!globalParams.isCE"
                    >
                    <span>{{$t('send_realtime_to_conversation')}}</span>
                    <i @click="realtimeTransfer" class="icon iconfont iconcamera fl" slot="reference"></i>
                </el-popover>
                <el-popover
                    placement="top"
                    trigger="hover"
                    popper-class="toolbar_item"
                    >
                    <span>{{$t('setting_title')}}</span>
                    <i @click="openGroupSetting" class="icon iconfont iconsetting fl" slot="reference"></i>
                </el-popover>
                <el-popover
                    placement="top"
                    trigger="hover"
                    popper-class="toolbar_item"
                    v-show="chatType===CHAT_TYPE['CHAT_WINDOW']&&!isService"
                    >
                    <span>{{$t('export_image')}}</span>
                    <i @click="exportFile" class="fl icon iconfont icondownload" slot="reference"></i>
                </el-popover>
                <el-popover
                    placement="top"
                    trigger="hover"
                    popper-class="toolbar_item"
                    v-show="!isService"
                    >
                    <span>{{$t('group_add_attendee_title')}}</span>
                    <i @click="addAttendee" class="icon iconfont iconusers-medical fl" slot="reference"></i>
                </el-popover>
                <el-popover
                    placement="top"
                    trigger="hover"
                    popper-class="toolbar_item"
                    v-permission="{conversationPermissionKey: 'member.remove'}"
                    >
                    <span>{{$t('groupset_delete_attendee')}}</span>
                    <i @click="deleteAttendee" class="icon iconfont iconuser-delete fl" slot="reference"></i>
                </el-popover>
                <el-popover
                    placement="top"
                    trigger="hover"
                    popper-class="toolbar_item"
                    v-permission="{regionPermissionKey: 'live'}"
                    v-if="isGroupChat"
                    >
                    <span>{{$t('reserved_conference')}}</span>
                    <i @click="openReservedConference" class="icon iconfont iconyuyue fl" slot="reference"></i>
                </el-popover>
                <!-- <el-popover
                    placement="top"
                    trigger="hover"
                    popper-class="toolbar_item"
                    v-permission="{regionPermissionKey: 'live'}"
                    v-if="isGroupChat"
                    >
                    <span>{{$t('reserved_conference')}}</span>
                    <i @click="sendSyncAccountOrLiveToULinker" class="icon iconfont iconmobilefill fl" slot="reference"></i>
                </el-popover> -->
            </div>
            <div class="panel_warper"
                @dragover.prevent="handleDragOver"
                @dragleave.prevent="handleDragLeave"
                @drop.prevent="handleDrop"
                :style="dragAreaStyle"
                >
                <div v-if="quote_message" class="quoted-message">
                    <div class="quote-header">
                        <span class="quote-sender">{{ quote_message.nickname }}:</span>
                        <span class="quote-close" @click="clearQuotedMessage">×</span>
                    </div>
                    <!-- 图片类型引用 -->
                    <div v-if="[systemConfig.msg_type.Image, systemConfig.msg_type.Frame, systemConfig.msg_type.OBAI].includes(quote_message.msg_type)" class="quote-content image-content" @click="openQuotedGallery(quote_message)">
                        <img :src="quote_message.url" alt="引用图片">
                        <span>{{ $t('msg_type_image') }}</span>
                    </div>
                    <!-- 视频类型引用 -->
                    <div v-else-if="[systemConfig.msg_type.Video, systemConfig.msg_type.Cine, systemConfig.msg_type.RealTimeVideoReview, systemConfig.msg_type.VIDEO_CLIP].includes(quote_message.msg_type)" class="quote-content video-content" @click="openQuotedGallery(quote_message)">
                        <div class="video-thumbnail">
                            <img v-if="quote_message.coverUrl || quote_message.url" :src="quote_message.coverUrl || quote_message.url" alt="视频封面">
                            <div class="video-play-icon">
                                <i class="iconfont iconbofang svg_icon_play"></i>
                            </div>
                        </div>
                        <span>{{ $t('msg_type_video') }}</span>
                    </div>
                    <!-- 文本类型引用 -->
                    <div v-else class="quote-content" v-html="quote_message.msg_body"></div>
                </div>
                <el-input
                    ref="edit_content"
                    v-model="textContent"
                    type="textarea"
                    :rows="5"
                    resize="none"
                    @keydown.enter.native="handleEnterKey"
                    @keydown.delete.native="handleDelete"
                    @keydown.backspace.native="handleDelete"
                    @input="handleInput"
                    @focus="handleFocus"
                    @click="handleClick"
                    @contextmenu.native="callTextAreaMenu($event)"
                    class="writing_panel"
                ></el-input>
            </div>
            <div class="send_wraper clearfix fr">
                <i @click="submitTextMessage" class="icon iconfont iconsend fl"></i>
                <el-popover
                    placement="top"
                    trigger="click"
                    popper-class="send_setting"
                    v-model="isShowSendSetting"
                    >
                    <i :class="{type2:sendType==2}" class="icon iconfont iconright"></i>
                    <p @click="switchSendType(1)">{{$t('send_by_enter')}}</p>
                    <p @click="switchSendType(2)">{{$t('send_by_ctrl_enter')}}</p>
                    <i slot="reference" class="icon iconfont iconsanjiaoxing fl"></i>
                </el-popover>
            </div>
        </div>
        <template v-if="isShowMention">
            <mention-dialog :show.sync="isShowMention" :callback="mentionCb"></mention-dialog>
        </template>
        <LiveConferenceSetting ref="liveConferenceSetting" v-model="liveConferenceSettingVisible" @liveConferenceSettingSubmit="clickStartConferenceFn"></LiveConferenceSetting>
        <LiveAddressDialog ref="liveAddressDialog" :cid="cid"></LiveAddressDialog>
    </div>
</template>
<script>
import base from '../lib/base'
import mentionDialog from './mentionDialog.vue'
import sendMessage from '../lib/sendMessage'
import ChatToolbar from './ChatToolbar.vue'
import record from '../lib/record_sound.js'
import Tool from '@/common/tool.js'
import { cloneDeep } from 'lodash'
import {
    htmlEscape,
    getLiveRoomObj,
    getLocalAvatar,
} from '../lib/common_base'
import LiveConferenceSetting from './live/liveConferenceSetting.vue'
import LiveAddressDialog from './live/liveAddressDialog.vue'
import moment from "moment";
import service from "../service/service";
import chatMessageList from '../components/chatMessageList.vue'
// import AttendeeList from '../components/attendeeList.vue'
import { CHAT_TYPE, EMOJI_LIST} from '../lib/constants'
import { getLanguage } from '@/common/i18n'
import permissionManager from '@/common/permission/PermissionManager.js'
export default {
    mixins: [base,record ,sendMessage],
    name: 'ChatComponent',
    permission: true, // 启用权限混入
    props:{
        chatType:{
            type:Number,
            default:CHAT_TYPE['CHAT_WINDOW']//组件类型，1在chatwindow页面，2在画廊
        },
        from:{
            type: String,
            default: 'index'
        },
        cid:{
            type:[Number,String],
            default:0
        }
    },
    components: {
        chatMessageList,
        mentionDialog,
        LiveConferenceSetting,
        LiveAddressDialog,
        // AttendeeList,
        ChatToolbar,
    },
    data(){
        return {
            CHAT_TYPE,
            getLocalAvatar,
            sendType:1,//触发发送的方式，1：enter，2：ctrl+enter
            isShowSendSetting:false,
            faceArr:['01','02','03','04','05','06','07','08','09','10','11','12','13','14','15','16','17','18','19','20','21','22','23','24','25','26','27','28','29','30','31','32','33','34','35','36','37','38','39','40','41','42','43','44','45','46','47','48','49','50','51','52','53','54','55','56','57','58','59','60','61',],
            isShowFacePanel:false,
            file_tag:0,
            isShowGroupSetting:false,
            isShowMention:false,
            chatMessageList:[],
            resourceIdList:[], // 现有资源id列表
            resourceMsgTypeList:[],// 可显示的引用资源类型
            isNeedLoadResourceStatus:false,// 是否需要更新引用资源为失效类型
            isOpenModeratorWantOpenMicTips:false,//是否弹起了主讲人希望发言弹窗
            isConferenceAuxOnline:0,//是否直播中（辅流在线代表在线）
            isConferenceMainOnline:0,//主流是否在房间
            isConferenceVideoStream:0,//是否直播开启视频
            isConferenceAudioStream:0,//是否直播开启声音
            isConferenceRecording:false,//是否正在录制视频
            localStreamStatus:{},//本地流状态
            liveConferenceSettingVisible:false,
            obstetric_qc_multicenter:null,
            multicenter_list:this.$store.state.multicenter.list,
            multicenter_config:this.$store.state.multicenter.config,
            multicenter_types:this.$store.state.multicenter.type,
            attendeeArray:[],
            currentGalleryList:[],//当前画廊的列表
            atUser:[],//
            dragging:false,
            quote_message: null, // 被引用的消息
            emojiArr: EMOJI_LIST,
            textContent: '', // 新增用于存储文本内容的数据
            currentCursorPosition: 0,
            previousContent: '', // 新增用于存储上一次的文本内容
            lastCursorPosition: 0, // 新增：记录最后的光标位置
        }
    },

    computed:{
        conversation(){
            return this.conversationList[this.cid]||{}
        },
        controller(){
            return this.conversation.socket;
        },
        realtimeVoice(){
            return this.$store.state.realtimeVoice[this.cid]||{}
        },
        isGroupChat(){
            return this.conversation.is_single_chat==0;
        },
        isWorkStation(){
            return this.isCef&&Tool.ifAppWorkstationClientType(this.systemConfig.clientType)
        },

        isService(){
            return this.conversation.service_type!=0
        },
        isShowExamType(){
            return this.conversation.service_type==this.systemConfig.ServiceConfig.type.FileTransferAssistant||this.conversation.service_type==0||(this.conversation.service_type==this.systemConfig.ServiceConfig.type.AiAnalyze)||(this.conversation.service_type==this.systemConfig.ServiceConfig.type.DrAiAnalyze)
        },
        dragAreaStyle() {
            return {
                border: this.dragging ? '2px solid #4285f4' : '', // 当拖动时边框颜色变化
                backgroundColor: this.dragging ? '#e8f0fe' : '' // 当拖动时背景颜色变化
            };
        },
        showStopConference(){
            return this.isConferenceAuxOnline
        },
        isStartLiveUser(){
            return this.LiveConferenceData.isHost
        },
        showMangerStopConference(){
            // 依赖会话权限版本号确保响应式更新
            this.conversationPermissionVersion;

            return permissionManager.checkConversationPermission('conference.stop', {
                conversationId: this.cid,
            })&&!this.isStartLiveUser&&this.isConferenceAuxOnline
        },
        remarkMap(){
            return this.$store.state.friendList.remarkMap;
        },

        liveConference(){
            return this.$store.state.liveConference
        },
        existLive(){
            return this.liveConference[this.cid]&&this.liveConference[this.cid].conferenceState
        },
        isDrAiAnalyze(){
            return this.conversation.service_type==this.systemConfig.ServiceConfig.type.DrAiAnalyze
        },
        isObstetricQCMulticenter(){
            return this.conversation.multicenter_type == 'obstetric_qc_multicenter'
        },
        isShowCallVideoBtn(){
            return this.$checkPermission({regionPermissionKey: 'live'})&&!this.existLive&&!this.isConferenceAuxOnline&&!this.isService&&this.$route.name === 'index'
        },
        isPCBrowser(){
            return this.systemConfig.client_type.Client == window.clientType;
        },
        EnableQc_statistics(){
            return this.$checkPermission({regionPermissionKey: 'qcStatistics'})&&this.$store.state.systemConfig.serverInfo.qc_statistics
                &&this.$store.state.systemConfig.serverInfo.qc_statistics.enable
        },
        isInternalNetworkEnv(){
            return this.systemConfig.serverInfo.network_environment
        },
        LiveConferenceData(){
            return this.$store.state.liveConference[this.cid]&&this.$store.state.liveConference[this.cid].LiveConferenceData || {}
        },
    },
    watch:{
        'conversation.chatMessageList':{
            handler(value,oldValue){
                if(!value){
                    return
                }
                // if(this.$route.name === 'gallery'&&this.from ==='gallery'){
                //     this.updateChatMessageList(value)
                // }else if(this.$route.name === 'index' &&this.from === 'index'){
                //     this.updateChatMessageList(value)
                // }else if(this.$route.name&&this.$route.name.includes('conference') && this.from === 'conference'){
                //     this.updateChatMessageList(value)
                // }else if(this.from === 'liveRoomWeb'){
                //     this.updateChatMessageList(value)
                // }
                this.updateChatMessageList(value)
            },
            deep:true,
            immediate:true
        },
        '$route.name':{
            handler(value,oldValue){
                if(oldValue){
                    if(oldValue.includes('conference')){
                        this.$refs.liveAddressDialog&&this.$refs.liveAddressDialog.closeLiveAddrPage()
                    }
                }

            },
            immediate:true
        },
        'LiveConferenceData':{
            handler(value){
                if(!value){
                    return
                }
                if(value.joinedMain){
                    this.isConferenceMainOnline = 1
                }else{
                    this.isConferenceMainOnline = 0
                }
                if(value.joinedAux){
                    this.isConferenceAuxOnline = 1
                }else{
                    this.isConferenceAuxOnline = 0
                }
                if(value.roomUserMap&&value.roomUserMap[value.localAuxUid]){
                    this.isConferenceVideoStream = value.roomUserMap[value.localAuxUid].videoStream
                    this.isConferenceAudioStream = value.roomUserMap[value.localAuxUid].audioStream
                }else{
                    this.isConferenceVideoStream = 0
                    this.isConferenceAudioStream = 0
                }
                this.isConferenceRecording = value.isRecording
            },
            deep:true,
            immediate:true
        },
        'conversation.attendeeList':{
            handler(value){
                console.log(this.$route.name,'this.$route.name')
                let list=cloneDeep(this.parseObjToArr(value));

                let filterList = [];
                let onlineList=[];
                let offlineList=[];
                //后端把所有用户都返回回来，前端只显示未退群用户
                for(let i=0; i<list.length; i++){
                    if(list[i].attendeeState != 0){
                        if(list[i].state==1){
                            onlineList.push(list[i])
                        }else{
                            offlineList.push(list[i])
                        }
                    }
                }

                this.attendeeArray = onlineList.concat(offlineList);
                // this.$set(this,'attendeeArray',filterList)
            },
            deep:true,
            immediate:true
        },
        'cid': {
            handler(val, oldVal) {
                if(Number(val)){
                    this.loadChatMessageList(val)
                    oldVal && this.$root.eventBus.$off(`${oldVal}_gateway_connect`);
                    val && this.$root.eventBus.$on(`${val}_gateway_connect`, this.gateWayConnect);
                }
            },
            immediate: true,
        }

    },
    created(){
        const msgType = this.$store.state.systemConfig.msg_type
        this.resourceMsgTypeList = [msgType.Image,msgType.Video,msgType.COMMENT,msgType.TAG,msgType.Frame]
    },
    mounted(){
        let that=this;
        this.sendType=window.localStorage.getItem('sendType')||1;
        this.initHideToolbarPanel();
        if (this.chatType===CHAT_TYPE['CHAT_WINDOW']) {
            //只在Index页面绑定事件
            //滚动到搜索消息,当搜索的文件就在当前窗口时执行，否则忽略
            this.$root.eventBus.$off('acceptLiveConference').$on('acceptLiveConference',that.acceptLiveConference);
            this.$root.eventBus.$off('closeLiveConference').$on('closeLiveConference',that.closeLiveConference);
            this.$root.eventBus.$off('cancelRealtimeVoice').$on('cancelRealtimeVoice',that.cancelRealtimeVoice);
            this.$root.eventBus.$off('StopConference').$on('StopConference',function(){
                console.log("on StopConference");
                that.stopConference();
            });
            this.$root.eventBus.$off('notifyStopUltrasoundDesktop').$on('notifyStopUltrasoundDesktop',that.notifyStopUltrasoundDesktop);
            this.$root.eventBus.$off('handleUploadStatus').$on('handleUploadStatus',that.handleUploadStatus);
            this.$root.eventBus.$off('handleCancelUpload').$on('handleCancelUpload',that.handleCancelUpload);
            this.$root.eventBus.$off('notifyWithdrawChatMessage').$on('notifyWithdrawChatMessage',that.handleWithdrawChatMessage);
            this.$root.eventBus.$off('notifyDeleteChatMessages').$on('notifyDeleteChatMessages',that.handleDeleteChatMessages);
            // 监听引用消息事件
            this.$root.eventBus.$off('quoteMessage').$on('quoteMessage', this.handleQuoteMessage);
        }
    },
    beforeDestroy(){
        this.$root.eventBus.$off('pasteClipboardMsg');
    },
    methods:{
        gateWayConnect() {
            if (Number(this.cid)) {
                this.reloadHistoryList(this.cid);
            }
        },
        async getChatMessageList(cid = this.cid) {
            try {
                const res = await Tool.handleAfterConversationCreated(cid);
                if (
                    !this.$store.state.conversationList[cid] ||
                    !this.$store.state.conversationList[cid].is_loaded_history_list
                ) {
                    this.$store.commit('conversationList/updateMessageList',{
                        list:[],
                        cid:cid
                    })
                    this.$root.eventBus.$emit("getMoreHistory", { sort: 0, cid,start:0 });
                }
            } catch (error) {
                console.error(error);
            }
        },
        async reloadHistoryList(cid){
            await Tool.handleAfterConversationCreated(cid);
            this.$root.eventBus.$emit("reloadHistoryList", cid );
        },
        shouldScrollBottom(){
            this.$refs.chat_message_list.shouldScrollBottom()
        },
        scrollToBottom(){
            this.$refs.chat_message_list.scrollToBottom()
        },
        goToDrAiAnalyzeStatistics(){
            this.$router.push(this.$route.fullPath+'/dr_ai_statistics')
        },
        goToMulticenter(){
            if(!this.conversation || !this.conversation.multicenter_info){
                return
            }
            let item = this.conversation.multicenter_info
            let option = {cid:this.cid, fid:0}
            if(this.conversation && this.conversation.is_single_chat){
                option.fid = this.conversation.fid
            }

            let type   = this.multicenter_types[item.type];
            let config = this.multicenter_config[item.type];
            let route  = config.roleRoute[item.userInfo.role];
            this.title  = this.$t(type);

            this.$store.commit('multicenter/setCurrentConfig',config);
            this.$store.commit('multicenter/updateEnterByGroup',option);
            this.$store.commit('multicenter/setCurrentMulticenter',item);
            this.$store.commit('multicenter/setAnonymous',item);
            this.$router.push(`/main/index/chat_window/${this.cid}/multicenter`)
        },
        initHideToolbarPanel(){
            document.addEventListener('click',(e)=>{
                if (e.target.className!='face_panel') {
                    this.isShowFacePanel=false
                }
                if (e.target.className!='group_setting_panel') {
                    this.isShowGroupSetting=false
                }
            })
        },
        switchSendType(type){
            this.sendType=type;
            window.localStorage.setItem('sendType',type)
            this.isShowSendSetting=false;
        },
        appendFace(str){
            this.insertHtmlAtCursor(str)
        },
        pasteHtml(event){
            const text = event.clipboardData.getData('text/plain');
            const cursorPosition = this.$refs.edit_content.$refs.textarea.selectionStart;
            this.textContent = this.textContent.slice(0, cursorPosition) + text + this.textContent.slice(cursorPosition);
        },
        insertHtmlAtCursor(html) {
            // 使用记录的最后光标位置
            const insertPosition = this.lastCursorPosition || 0;
            this.textContent = this.textContent.slice(0, insertPosition) + html + this.textContent.slice(insertPosition);

            // 更新光标位置
            this.$nextTick(() => {
                const newPosition = insertPosition + html.length;
                this.$refs.edit_content.$refs.textarea.selectionStart = newPosition;
                this.$refs.edit_content.$refs.textarea.selectionEnd = newPosition;
                this.lastCursorPosition = newPosition; // 更新最后的光标位置

                // 重新聚焦输入框
                this.$refs.edit_content.focus();
            });
        },
        exportFile(){
            if (this.globalParams.isCef) {
                Tool.loadModuleRouter(this.$route.fullPath+'/export_file')
            }else{
                this.$message.error(this.$t('use_app_tip'))
            }
        },
        clearHistory(){
            this.$store.commit('conversationList/clearHistory',{
                cid:this.cid
            })
        },
        openChatHistorySearch(){
            Tool.loadModuleRouter(this.$route.fullPath+'/chat_history_search_list')
        },
        showGroupSetting(){
            this.isShowGroupSetting=true;
            this.isShowFacePanel=false;
        },
        showFacePanel(){
            // 在显示表情面板前记录当前光标位置
            this.lastCursorPosition = this.$refs.edit_content.$refs.textarea.selectionStart;
            this.isShowFacePanel = true;
            this.isShowGroupSetting = false;
        },
        openGroupSetting(){
            Tool.loadModuleRouter(this.$route.fullPath+'/edit_group_setting')
        },
        addAttendee(){
            Tool.loadModuleRouter(this.$route.fullPath+'/add_attendee')
        },
        deleteAttendee(){
            Tool.loadModuleRouter(this.$route.fullPath+'/delete_attendee')
        },
        togglePageType(){
            this.$root.eventBus.$emit('togglePageType')
        },
        getLiveAddress(){
            this.$refs.liveAddressDialog.loadAddressDialog()
        },
        fileTransfer(){
            if(!Tool.checkSpeakPermission(this.cid, this.user.uid)){
                this.$message.error(this.$t('no_speak_permission'));
                return;
            }
            if (this.enableFileTransfer()) {
                // this.scrollToBottom();
                window.CWorkstationCommunicationMng.addExam(this.cid);
            }
        },
        realtimeTransfer(){
            if(!Tool.checkSpeakPermission(this.cid, this.user.uid)){
                this.$message.error(this.$t('no_speak_permission'));
                return;
            }
            if (this.enableFileTransfer()) {
                this.$store.commit('globalParams/updateGlobalParams', {
                    realtime_ultrasound_mode:true
                })
                window.CWorkstationCommunicationMng.realtimeUltrasound({
                    consultation_id:parseInt(this.cid),
                    top:0,
                    left:0,
                    width:100,
                    height:100,
                    dpr:1
                });
            }
        },
        enableFileTransfer(){
            if(false == this.systemConfig.serverInfo.enable_file_transfer_function){
                this.$message.error(this.$t('security_restrictions'))
                return false;
            }else{
                return true;
            }
        },
        micErrorChecking(){
            Tool.loadModuleRouter(this.$route.fullPath+"/equipment_detection/" + this.cid);
        },

        openReservedConference(){
            Tool.loadModuleRouter(this.$route.fullPath+"/reserved_conference")
        },
        handleInput(value) {
            // 获取当前输入长度
            const currentLength = value.length;

            // 更新最后的光标位置
            this.lastCursorPosition = this.$refs.edit_content.$refs.textarea.selectionStart;

            // 如果之前没有存储上一次的文本内容，初始化它
            if (!this.previousContent) {
                this.previousContent = '';
            }

            // 判断是输入还是删除
            const isTyping = currentLength > this.previousContent.length;

            // 检测是否输入了@
            if (value.slice(-1) === '@' && this.isGroupChat && isTyping) {
                this.isShowMention = true;
                this.$refs.edit_content.blur();
                this.currentCursorPosition = this.lastCursorPosition;
            }

            // 保存当前内容用于下次比较
            this.previousContent = value;
        },
        handleDelete(e) {
            const cursorPosition = this.$refs.edit_content.$refs.textarea.selectionStart;
            const text = this.textContent;

            // 查找所有@用户的位置
            const matches = [];
            let match;
            const regex = /@[\w\u4e00-\u9fa5]+\s/g;

            while ((match = regex.exec(text)) !== null) {
                matches.push({
                    start: match.index,
                    end: match.index + match[0].length,
                    text: match[0]
                });
            }

            // 检查光标是否在某个@用户内
            for (const match of matches) {
                if (cursorPosition > match.start && cursorPosition <= match.end) {
                    // 删除整个@用户
                    const newText = text.slice(0, match.start) + text.slice(match.end);
                    this.textContent = newText;

                    // 从atUser中移除该用户
                    const nickname = match.text.slice(1).trim();
                    this.atUser = this.atUser.filter(item => item.nickname !== nickname);

                    // 更新 previousContent，确保下次输入@时能正确触发
                    this.previousContent = newText;

                    // 设置光标位置
                    this.$nextTick(() => {
                        this.$refs.edit_content.$refs.textarea.selectionStart = match.start;
                        this.$refs.edit_content.$refs.textarea.selectionEnd = match.start;
                        this.lastCursorPosition = match.start; // 同时更新最后的光标位置
                    });

                    e.preventDefault();
                    return;
                }
            }
        },
        mentionCb(list) {
            this.$refs.edit_content.focus();
            this.atUser = list;

            // 在@符号位置插入用户名
            const beforeText = this.textContent.slice(0, this.currentCursorPosition - 1); // 减1删除触发@的符号
            const afterText = this.textContent.slice(this.currentCursorPosition);

            // 构建@用户字符串
            const mentionText = list.map(item => `@${item.nickname}`).join(' ') + ' ';
            this.textContent = beforeText + mentionText + afterText;

            // 设置光标位置到所有@用户之后
            this.$nextTick(() => {
                const newPosition = this.currentCursorPosition - 1 + mentionText.length;
                this.$refs.edit_content.$refs.textarea.selectionStart = newPosition;
                this.$refs.edit_content.$refs.textarea.selectionEnd = newPosition;
            });
        },
        judgeIfResourceExpiration(chatItem){
            if(chatItem.hasOwnProperty('resource_id')&&!this.resourceIdList.includes(chatItem.resource_id)&&chatItem.been_withdrawn!=1&&chatItem.been_withdrawn!=2){
                return true
            }else{
                return false
            }
        },
        changeChatListResourceStatus(value){
            if(!Array.isArray(value)){
                return
            }
            let resourceList = value
            this.resourceIdList = []
            resourceList.forEach(item=>{
                if(!this.resourceIdList.includes(item.resource_id)){
                    if(item.resource_id !== null){
                        this.resourceIdList.push(item.resource_id)
                    }
                }
            })
            let chatMessageList=this.chatMessageList;
            if(chatMessageList.length === 0){ //聊天列表还未加载，需要在加载后手动触发
                this.isNeedLoadResourceStatus = true
                return
            }
            for(let i=chatMessageList.length-1; i>=0; i--){
                if(chatMessageList[i].hasOwnProperty('resource_id')&&!this.resourceIdList.includes(chatMessageList[i].resource_id)&&this.resourceMsgTypeList.includes(chatMessageList[i].msg_type)){
                    if(chatMessageList[i].been_withdrawn!=1&&chatMessageList[i].been_withdrawn!=2){
                        this.$set(chatMessageList[i],'msg_type',this.$store.state.systemConfig.msg_type.EXPIRATION_RES)
                    }
                    // else if(chatMessageList[i].hasOwnProperty('resource_id')&&chatMessageList[i].resource_id === null){
                    //     Vue.set(chatMessageList[i],'msg_type',this.$store.state.systemConfig.msg_type.EXPIRATION_RES)
                    // }

                }
            }
            //
        },
        updateChatMessageList(value) {
            if (Array.isArray(value)) {
                const assignedIndexes = new Set(); // 用于跟踪所有已存在的 tmp_index

                // 先收集所有已有的 tmp_index
                value.forEach(item => {
                    if (item.tmp_index !== undefined) {
                        assignedIndexes.add(item.tmp_index);
                    }
                });

                const newChatMessageList = value
                    .map((item, index, array) => {
                        // 计算从尾部开始的索引
                        let reverseIndex = array.length - 1 - index;

                        // 如果 item.tmp_index 未定义，则为其分配 reverseIndex
                        // 并确保分配的 reverseIndex 不重复
                        if (item.tmp_index === undefined) {
                            while (assignedIndexes.has(reverseIndex)) {
                                reverseIndex--; // 递减 reverseIndex 直到找到未使用的值
                            }
                            item.tmp_index = reverseIndex;
                            assignedIndexes.add(reverseIndex); // 记录已使用的 tmp_index
                        }

                        // 处理 msg_type
                        const msg_type = item.been_withdrawn == 2
                            ? this.systemConfig.msg_type.WITHDRAW
                            : item.msg_type;
                        return {
                            ...item,
                            msg_type,
                        };
                    });

                // 使用 Vue.set 确保响应式更新
                this.$set(this, 'chatMessageList', newChatMessageList);

                // 添加调试日志验证更新
                console.log('chatMessageList updated via $set, length:', this.chatMessageList.length);

                // 强制触发下一个tick的更新
                this.$nextTick(() => {
                    console.log('nextTick after chatMessageList update, length:', this.chatMessageList.length);
                });

                this.isNeedLoadResourceStatus = false;
            }
        },
        changeOwnCameraStatus() {
            this.toggleOwnCameraStatus()
        },
        startCameraFn(){
            this.startCamera(this.cid)
        },
        async clickStartConferenceFn(){
            console.log('clickStartConferenceFn', this.$checkPermission({regionPermissionKey: 'webShareScreen'}));
            if(Tool.checkAppClient('Browser')&&!this.$checkPermission({regionPermissionKey: 'webShareScreen'})){
                this.$message.error(this.$t('banned_this_moment'))
                return
            }
            if(this.$route.name=='gallery'){
                this.$root.eventBus.$emit('closeGallery');
            }
            const defaultPushWay = window.localStorage.getItem('defaultPushWay')
            const bindULinkerDeviceId = this.getBindULinkerDeviceIdFromStorage()
            if(bindULinkerDeviceId){
                // this.$root.eventBus.$emit('chatWindowStartJoinRoom',{main:0,aux:1,isSender:1})
                this.sendSyncAccountOrLiveToULinker(this.cid)
            }else{
                this.$root.eventBus.$emit('chatWindowStartJoinRoom',{main:1,aux:1,isSender:1,videoSource:defaultPushWay?defaultPushWay:'doppler'})

            }


        },
        openLiveConferenceSetting(){
            this.liveConferenceSettingVisible = true
        },
        acceptLiveConference(cid){
            if (this.$route.meta.inChatWindow||this.$route.meta.inTvWall) {
                this.$root.eventBus.$emit('chatWindowStartJoinRoom',{main:0,aux:1,isSender:0})
            }

        },
        forceStopConference(){
            this.$MessageBox.confirm(this.$t('is_force_conference_by_group_owner'), this.$t('tip_title'), {
                confirmButtonText: this.$t('confirm_txt'),
                callback: action => {
                    if(action==='confirm'){
                        let liveRoom = getLiveRoomObj()
                        if(!liveRoom){
                            return
                        }
                        liveRoom.LeaveChannelAux('groupOwner')
                    }
                }
            });
        },
        sendRecordVoice(){
            if(Number(window.vm.$root.currentLiveCid) === Number(this.cid)){
                this.$message.error(this.$t('playing_video_tip'))
                return
            }
            this.recordStart()
        },
        openExamImages(message){
            //直播中不打开病例
            if(Number(window.vm.$root.currentLiveCid) === Number(this.cid)){
                this.$message.error(this.$t('playing_video_tip'))
                return
            }
            this.$router.push(`/main/index/chat_window/${this.cid}/exam_image_list/${message.gmsg_id}`);
        },
        requestRemoteControl(){
            let liveRoom = getLiveRoomObj()
            if(!liveRoom){
                return
            }
            liveRoom.requestRemoteControl()
        },
        openBIDataShow(){
            var that = this;
            console.log('group_id:',this.cid)
            const requestConfig = this.systemConfig.server_type
            let ajaxServer= requestConfig.protocol+requestConfig.host+requestConfig.port;

            if(process.env.NODE_ENV === 'production'){
                ajaxServer += '/statistic'
            }else{
                ajaxServer = window.location.origin
            }
            const url = Tool.transferLocationToCe(`${ajaxServer}/statistic.html#/remote_ultrasound_data_center?dataFrom=group&id=${this.cid}&token=${window.vm.$store.state.dynamicGlobalParams.token}&language=${getLanguage()}`)
            if(Tool.ifBrowserClientType(that.systemConfig.clientType)) {
                window.open(url,'blank');
            } else {
                window.CWorkstationCommunicationMng.OpenNewWindow({url})
            }
        },
        handleDragOver(event) {
            this.dragging = true; // 开始拖动时设置dragging为true
        },
        handleDragLeave(event) {
            this.dragging = false; // 当文件离开拖放区域时重置
        },
        handleDrop(event){
            this.dragging = false;
            const files = event.dataTransfer.files;
            console.log(files)
            this.uploadPictureStart(files)
        },
        openCloudExam(){
            this.$router.push({
                path: `${this.$route.path}/cloud_exam`,
                query: this.$route.query,
            });
        },
        async loadChatMessageList(cid){
            this.openConversation(cid,10,null,(is_suc)=>{
                if(is_suc){
                    if(this.conversationList[cid].is_need_reload){
                        this.reloadHistoryList(cid)
                    }else{
                        this.getChatMessageList(cid);
                    }
                }
            })
        },
        handleEnterKey(e) {
            // 如果按下了shift键或者(enter发送模式下按下ctrl键),直接插入换行
            if (e.shiftKey || (this.sendType == 1 && e.ctrlKey)) {
                const cursorPosition = this.$refs.edit_content.$refs.textarea.selectionStart;
                this.textContent = this.textContent.slice(0, cursorPosition) + '\n' + this.textContent.slice(cursorPosition);
                this.$nextTick(() => {
                    // 将光标移动到换行后的位置
                    this.$refs.edit_content.$refs.textarea.selectionStart =
                    this.$refs.edit_content.$refs.textarea.selectionEnd = cursorPosition + 1;
                });
                e.preventDefault();
                return;
            }

            // 处理普通enter键
            const emitType = e.ctrlKey ? 2 : 1;
            if (emitType == this.sendType) {
                // 如果按键组合匹配发送设置，则发送消息
                e.preventDefault();
                this.submitTextMessage();
            } else {
                // 如果是 ctrl+enter 发送模式下的普通 enter，则插入换行
                if (this.sendType == 2) {
                    const cursorPosition = this.$refs.edit_content.$refs.textarea.selectionStart;
                    this.textContent = this.textContent.slice(0, cursorPosition) + '\n' + this.textContent.slice(cursorPosition);
                    this.$nextTick(() => {
                        // 将光标移动到换行后的位置
                        this.$refs.edit_content.$refs.textarea.selectionStart =
                        this.$refs.edit_content.$refs.textarea.selectionEnd = cursorPosition + 1;
                    });
                    e.preventDefault();
                }
            }
        },
        submitTextMessage() {
            if(!Tool.checkConversationConnect(this.cid)){
                this.$message.error(this.$t('network_error_tip'))
                return
            }
            if(!Tool.checkSpeakPermission(this.cid, this.user.uid)){
                this.$message.error(this.$t('no_speak_permission'));
                return;
            }

            let text = this.textContent.trim();
            if (text == '') {
                return;
            }

            this.$root.isScrollingList[this.cid] = false;
            console.log('quote_message',this.quote_message)
            if(this.quote_message){
                if(!this.atUser.includes(this.quote_message.sender_id)){
                    this.atUser.push({
                        uid:this.quote_message.sender_id,
                        nickname:this.quote_message.sender_name
                    })
                }
                this.sendTextMessage({text,quote_message:this.quote_message});
                this.quote_message = null; // 发送后清空引用消息
            }else{
                this.sendTextMessage({text});
            }

            this.textContent = ''; // 清空输入框
            this.atUser = []; // 清空@用户列表
            this.scrollToBottom();
        },
        handleFocus() {
            this.lastCursorPosition = this.$refs.edit_content.$refs.textarea.selectionStart;
        },
        handleClick() {
            this.lastCursorPosition = this.$refs.edit_content.$refs.textarea.selectionStart;
        },
        callTextAreaMenu(event){
            // if(Tool.checkAppClient('Cef')){
            //     event.preventDefault();
            //     // 保存当前光标位置
            //     if (this.$refs.edit_content && this.$refs.edit_content.$refs.textarea) {
            //         this.lastCursorPosition = this.$refs.edit_content.$refs.textarea.selectionStart;
            //     }

            //     this.$root.eventBus.$emit('showTextAreaMenu', {
            //         event: event,
            //         textarea: this.$refs.edit_content.$refs.textarea,
            //         textContent: this.textContent,
            //         updateFunc: (newText) => {
            //         // 更新文本内容
            //             this.textContent = newText;
            //             // 记住最后的光标位置供后续使用
            //             this.$nextTick(() => {
            //                 if (this.$refs.edit_content && this.$refs.edit_content.$refs.textarea) {
            //                     this.lastCursorPosition = this.$refs.edit_content.$refs.textarea.selectionStart;
            //                 }
            //             });
            //         }
            //     });
            // }

        },
        handleQuoteMessage(message) {
            if (!message) {
                return;
            }

            // 检查消息是否有有效的 gmsg_id
            if (!message.gmsg_id) {
                return;
            }

            // 设置被引用的消息
            this.quote_message = message;
            // 聚焦输入框
            this.$nextTick(() => {
                this.$refs.edit_content.focus();
                this.$refs.chat_message_list.scrollToBottom();
            });
        },
        clearQuotedMessage() {
            this.quote_message = null;
        },
        openQuotedGallery(message) {
            if(!message || !message.resource_id) {
                return;
            }

            if(Number(window.vm.$root.currentLiveCid) === Number(this.cid)){
                this.$message.error(this.$t('playing_video_tip'));
                return;
            }

            // 准备打开画廊
            this.$store.commit('gallery/setGallery',{
                list:[message],
                openFile:message,
                loadMore:false,
                loadMoreCallback:null
            });

            this.$nextTick(() => {
                this.$router.push(this.$route.fullPath+'/gallery');
            });
        },
        handleWithdrawChatMessage(data){
            console.log('handleWithdrawChatMessage', data);
            if (!data || typeof data.cid === 'undefined' || !Array.isArray(data.gmsg_id_list)) {
                return;
            }
            if (String(data.cid) !== String(this.cid)) {
                return;
            }
            if (!this.quote_message || typeof this.quote_message.gmsg_id === 'undefined') {
                return;
            }
            if (data.gmsg_id_list.includes(this.quote_message.gmsg_id)) {
                this.quote_message = null;
            }
        },
        handleDeleteChatMessages(data){
            console.log('handleDeleteChatMessages', data);

            if (!data || typeof data.cid === 'undefined' || !Array.isArray(data.gmsg_id_list)) {
                return;
            }
            if (String(data.cid) !== String(this.cid)) {
                return;
            }
            if (!this.quote_message || typeof this.quote_message.gmsg_id === 'undefined') {
                return;
            }
            if (data.gmsg_id_list.includes(this.quote_message.gmsg_id)) {
                this.quote_message = null;
            }
        }
    }
}
</script>
<style lang="scss">
.chat_mode{
    height:100%;
    display:flex;
    flex-direction: column;
    font-size:16px;
    .header{
        height:40px;
        text-align:center;
        color:#fff;
        background-color: #7ba7b4;
        line-height:40px;
        font-size:16px;
        padding:0 20px;
        font-weight: 600;
        color:#fff;

    }
    .chat_editer{
        min-height:220px;
        background-color: #F5F8FA;
        border-top: 0px solid #D9D9D9;
        position:relative;
        overflow:visible;
        z-index:2;
        padding-bottom: 20px;
        .videos{
            display:none;
        }
        .toolbar{
            user-select: none;
            border-top: 1px solid #ddd;
            i{
                font-size: 28px;
                line-height:44px;
                color: #9d9d9e;
                margin:0 6px;
                cursor:pointer;
            }
            .iconphone-1{
                color: rgb(212, 59, 71);
                font-weight: bold;
            }
            .iconphone-{
                color: #2d993b;
                font-weight: bold;
            }
            .icontv{
                color: #2d993b;
                font-weight: bold;
                font-size: 26px;
                line-height:40px;
            }
            .iconfolder,.iconplus1{
                font-size:26px;
            }
            .iconmicrophone{
                background: #a1b7b6;
                color: #fff;
                position:relative;
                &::after{
                    content:'';
                    display:block;
                    width:4px;
                    height:4px;
                    border-radius:50%;
                    background-color:#5bf961;
                    position:absolute;
                    top:4px;
                    left:4px;
                }

            }
            .video_ani{
                float:left;
            }
            .videoing{
                float:left;
            }
        }
        .panel_warper{
            padding:0 10px;
            display: flex;
            flex-direction: column;
            height: calc(100% - 86px);

            .quoted-message {
                margin-bottom: 5px;
                padding: 5px 8px;
                background-color: rgba(0, 0, 0, 0.03);
                border-left: 3px solid #c7c7c7;
                border-radius: 3px;
                position: relative;
                flex-shrink: 0;

                .quote-header {
                    display: flex;
                    align-items: center;
                    margin-bottom: 2px;
                    font-size: 12px;
                    color: #888;

                    .quote-sender {
                        margin-right: 5px;
                    }

                    .quote-close {
                        position: absolute;
                        right: 5px;
                        top: 5px;
                        width: 20px;
                        height: 20px;
                        line-height: 20px;
                        text-align: center;
                        cursor: pointer;
                        color: #999;
                        font-size: 16px;
                        font-weight: bold;
                        background-color: rgba(0, 0, 0, 0.05);
                        border-radius: 50%;

                        &:hover {
                            color: #555;
                            background-color: rgba(0, 0, 0, 0.1);
                        }
                    }
                }

                .quote-content {
                    font-size: 12px;
                    color: #666;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    max-width: 100%;

                    &.image-content, &.video-content {
                        display: flex;
                        align-items: center;
                        cursor: pointer;
                        transition: background-color 0.2s ease;

                        &:hover {
                            background-color: rgba(0, 0, 0, 0.05);
                            border-radius: 3px;
                        }
                    }

                    &.image-content {
                        display: flex;
                        align-items: center;

                        img {
                            width: 36px;
                            height: 36px;
                            object-fit: cover;
                            margin-right: 5px;
                            border-radius: 2px;
                        }
                    }

                    &.video-content {
                        display: flex;
                        align-items: center;

                        .video-thumbnail {
                            position: relative;
                            width: 60px;
                            height: 40px;
                            margin-right: 5px;
                            border-radius: 2px;
                            overflow: hidden;
                            background-color: #000;

                            img {
                                width: 100%;
                                height: 100%;
                                object-fit: contain;
                            }

                            .video-play-icon {
                                position: absolute;
                                top: 0;
                                left: 0;
                                width: 100%;
                                height: 100%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                i {
                                    font-size: 16px;
                                    color: #fff;
                                }
                            }
                        }
                    }
                }
            }

            .writing_panel {
                flex: 1;
                display: flex;

                .el-textarea__inner {
                    flex: 1;
                    padding: 6px;
                    border-radius: 4px;
                    height: 100% !important;
                    line-height: 20px;
                    transition: border linear 0.2s, box-shadow linear 0.2s;
                    outline: 0;
                    overflow: auto;
                    resize: none;
                    background-color: transparent;
                    border: none;
                    font-size: 14px;
                    color: #333;
                    word-break: break-all;
                    word-wrap: break-word;
                    min-height: 20px;

                    &:focus {
                        border-color: rgba(82, 168, 236, 0.8);
                        box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1), 0 0 8px rgba(82, 168, 236, 0.6);
                    }

                    &::placeholder {
                        color: #999;
                    }
                }
            }
        }
        .send_wraper{
            width:80px;
            height:40px;
            background-color:#d4b965;
            cursor:pointer;
            color: #fff;
            border-radius: 3px;
            margin-right:10px;
            margin-top:8px;
            &:hover{
                background-color:#af944a;
            }
            .iconsend{
                font-size: 24px;
                line-height: 40px;
                width: 60px;
                text-align: center;
                border-right: 1px solid;
            }
            .iconsanjiaoxing{
                line-height: 40px;
                font-size: 12px;
                text-align: center;
                width: 20px;
                transform: scale(0.8);
            }
        }
        .face_panel,.group_setting_panel{
            position:absolute;
            width:100%;
            bottom:220px;
            background: #a9bfbe;
            padding-top: 6px;
            z-index:3;
            .emoji {
                font-size: 18px; /* 调整表情符号的大小 */
                cursor: pointer;
                width: 34px;
                height: 34px;
                display: inline-block;
                line-height: 34px;
                text-align: center;
            }
            .emoji:hover {
                background-color: #f0f0f0; /* 鼠标悬停效果 */
                border-radius: 5px;
            }
            &>img{
                margin: 0px;
                padding: 4px;
                cursor:pointer;
                border: 1px solid #a9bfbe;
                &:hover{
                    border:1px solid #ccc;
                }
            }
            i{
                cursor:pointer;
                color:#fff;
                font-size:32px;
                margin:0 6px;
                line-height: 42px;
            }
            .iconusers-medical{
                font-size: 28px;
            }
            .iconyuyue{
                font-size: 30px;
            }
            .icon_chaoshenghuizhenshenqing{
                font-size: 30px;
            }
        }
        .recording_panel{
            position:absolute;
            width:100%;
            bottom:220px;
            background: #a9bfbe;
            height:46px;
            line-height:46px;
            color:#e5edf1;
            z-index:9;
            border: 1px solid rgb(255, 255, 255);
            .iconsend,.iconel-icon-delete2{
                float: right;
                width: 40px;
                font-size: 26px;
                cursor:pointer;
            }
            .record_count{
                float: left;
                margin-left: 50px;
                line-height: 1;
                i{
                    font-size:28px;
                }
                span{
                    display: inline-block;
                    line-height: 46px;
                    width: 60px;
                    text-align: center;
                    font-size: 20px;
                }
            }
        }
        .realtime_toolbar{
            position:absolute;
            bottom:220px;
            background: #a9bfbe;
            padding: 6px;
            white-space:normal;
            z-index:2;
            left: 0;
            width: 100%;
            display: flex;
            align-items: center;
            &>span{
                margin-right: 10px;
            }
            .redColor{
                color:red;
            }
            i{
                font-size: 28px;
                color: #fff;
                margin: 0 2px;
                cursor: pointer;
            }
            .iconuser-cog{
                font-size:26px;
                position:relative;
                span{
                    position: absolute;
                    width: 8px;
                    height: 8px;
                    background: #f00;
                    right: 4px;
                    top: 0px;
                    border-radius: 50%;
                }
            }
            .round_pot{
                width: 30px;
                vertical-align: sub;
            }
            .iconshut-down{
                color:#f00;
            }
            .iconmicrophone{
                position:relative;
                &:before{
                    color:#01c59d;
                }
                &:after{
                    content:"\e78b";
                    height:100%;
                    display:block;
                    position:absolute;
                    overflow:hidden;
                    top:0px;
                    left:0px;
                    color:#fff;
                    transition:height 0.1s;
                    line-height:30px;
                }
                &.volume_0:after{height:100%}
                &.volume_1:after{height:81%}
                &.volume_2:after{height:72%}
                &.volume_3:after{height:63%}
                &.volume_4:after{height:54%}
                &.volume_5:after{height:45%}
                &.volume_6:after{height:36%}
                &.volume_7:after{height:27%}
                &.volume_8:after{height:18%}
                &.volume_9:after{height:9%}
                &.volume_10:after{height:0%}
            }
            .iconyinliang-copy{
                position:relative;
                &:before{
                    color:#01c59d;
                }
                &:after{
                    content:"\e60c";
                    height:100%;
                    display:block;
                    position:absolute;
                    overflow:hidden;
                    top:0px;
                    left:0px;
                    color:#fff;
                    transition:height 0.1s;
                    line-height:30px;
                }
                &.volume_0:after{height:100%}
                &.volume_1:after{height:81%}
                &.volume_2:after{height:72%}
                &.volume_3:after{height:63%}
                &.volume_4:after{height:54%}
                &.volume_5:after{height:45%}
                &.volume_6:after{height:36%}
                &.volume_7:after{height:27%}
                &.volume_8:after{height:18%}
                &.volume_9:after{height:9%}
                &.volume_10:after{height:0%}
            }
            @keyframes star {
                10% {
                    opacity: 0;
                }

                90% {
                    opacity: 1;
                }
            }
            .iconagora_luzhizhong{
                color: red;
                font-size: 32px;
                font-weight: 600;
                animation: star 0.5s ease-in infinite;
            }
            .iconagora_luzhi{
                font-size: 32px;
                font-weight: 600;
            }
        }
        .realtime_toolbar_triangle{
            position: absolute;
            border: 10px solid #a9bfbe;
            width: 0;
            height: 0;
            border-bottom: none;
            border-left-color: transparent;
            border-right-color: transparent;
            bottom: 210px;
            left: 130px;
        }
    }
    &.gallery_type{
        .chat_history{
            .message_item{
                .message_item_wrapper{
                    background-color:#EEE8EE;
                    &:before{
                        border-bottom-color:#EEE8EE;
                    }
                }
            }
            .system_message{
                background-color:#EEE8EE;
                color:#333;
                max-width:80%;
            }
            .self_chat{
                .message_item_wrapper{
                    background-color:#EEE8EE;
                    &:before{
                        border-bottom-color:#EEE8EE;
                    }
                }

            }
        }
        .chat_editer{
            background-color:#fff;
            .toolbar{
                .icon{
                    color:#413a39;
                }
                .icontv{
                    color: #2d993b;
                }
                .iconphone-1{
                    color: rgb(212, 59, 71);
                    font-weight: bold;
                }
                .iconphone-{
                    color: #2d993b;
                    font-weight: bold;
                }
                .iconmicrophone{
                    background: #fff;
                }
            }
            .realtime_toolbar{
            }
        }
    }
    &.live_type, &.base_gallery_type{
        .chat_history{
            background-color:#fff;
        }
        .header{
            color:#000;
            background-color: #f5f5f5;
        }
        .realtime_toolbar{
            background: #f5f5f5;
            i{
                color: #413a39;
            }
        }
        .realtime_toolbar_triangle{

            position: absolute;
            width: 0;
            height: 0;
            border: 10px solid #D9D9D9;
            border-bottom: none;
            border-left-color: transparent;
            border-right-color: transparent;
            bottom: 210px;
            left: 130px;
        }
        .chat_editer{
            background-color:#ebeef5;
            .toolbar{
                .icon{
                    color:#413a39;
                }
            }
        }
    }

}
.toolbar_item_attendee{
    width:200px;
    height:300px;
}
.iworks_tree_popper{
    padding-right:30px;
}
.scroller{
    height: 100%;
    &.showPadding50{
        padding-bottom: 50px;
    }
    // overflow: auto;
    .scroller-item{
        min-height: 40px;
        width: 100%;
        // display: flex;
        // min-height: 150px;
        // padding: 12px;
        box-sizing: border-box;
    }
    .empty_item{
        height:60px;
    }
}
.vue-recycle-scroller::-webkit-scrollbar{
    width: 10px;
    height: 10px;
}
.vue-recycle-scroller::-webkit-scrollbar-track{
    background: rgb(239,239,239);
    border-radius: 2px;
}
.vue-recycle-scroller::-webkit-scrollbar-thumb{
    background: rgb(169,191,190);
    border-radius: 10px;
    cursor: pointer;
}
.record_start{
    width: 22px;
    height: 22px;
    background: red;
    border-radius: 50%;
    display: inline-block;
    cursor: pointer;
    position: relative;
    &::after{
        content:'';
        border-radius: 50%;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate3d(-50%,-50%,0);
        background: #fff;
        width: 8px;
        height: 8px;

    }
}
// .vue-recycle-scroller::-webkit-scrollbar-thumb:hover{
//     background: #333;
// }
// .vue-recycle-scroller::-webkit-scrollbar-corner{
//     background: #179a16;
// }


</style>
